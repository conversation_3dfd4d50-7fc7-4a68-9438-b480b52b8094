/* Removed CSS scroll-behavior to prevent conflicts with JavaScript smooth scrolling */

/* Add scroll padding to account for sticky navbar */
html {
  scroll-padding-top: 80px; /* Adjust based on navbar height */
}

/* Set background color for the entire template */
body {
  background-color: #CADCAE;
}


/* Custom styles for nav buttons */
.nav-btn {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.55);
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 400;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-btn:hover {
  color: rgba(0, 0, 0, 0.7);
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-btn.active {
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(0, 0, 0, 0.1);
}

.nav-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Animation for button press */
.nav-btn:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Ripple effect animation */
.nav-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.nav-btn:hover::before {
  width: 100%;
  height: 100%;
}

/* Ensure dropdown items are also styled */
.dropdown-item {
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  transform: translateX(5px);
  background-color: #f8f9fa;
}

/* Keyframe for ripple animation */
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}


.hero-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('../img/IMG-20250819-WA0065.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.hero-section h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    font-weight: 700;
}

.hero-section p {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    font-size: 1.25rem;
}

.btn-primary {
    padding: 0.75rem 2rem;
    border-radius: 30px;
}

/* Language Toggle Animations */
.language-toggle-container {
  position: relative;
}

.language-toggle-container svg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 3px;
  padding: 2px;
}

.language-toggle-container svg:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Flag animation states */
.flag-en {
  opacity: 1;
  transform: translateX(0);
}

.flag-id {
  opacity: 0.6;
  transform: translateX(0);
}

/* When Indonesian is selected */
.language-toggle-container.id-active .flag-en {
  opacity: 0.6;
  transform: translateX(-2px);
}

.language-toggle-container.id-active .flag-id {
  opacity: 1;
  transform: translateX(2px);
  box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.3);
}

/* When English is selected */
.language-toggle-container.en-active .flag-en {
  opacity: 1;
  transform: translateX(-2px);
  box-shadow: 0 0 0 2px rgba(1, 33, 105, 0.3);
}

.language-toggle-container.en-active .flag-id {
  opacity: 0.6;
  transform: translateX(2px);
}

/* Enhanced switch animation */
.form-check-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.form-check-input:checked {
  background-color: #dc3545;
  border-color: #dc3545;
  transform: scale(1.05);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-input:not(:checked) {
  background-color: #012169;
  border-color: #012169;
  transform: scale(1.05);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-input:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Pulse animation for active flag */
@keyframes flagPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.flag-active {
  animation: flagPulse 0.6s ease-in-out;
}

/* Smooth slide animation for switch */
@keyframes switchSlide {
  0% { transform: translateX(-10px); opacity: 0.8; }
  100% { transform: translateX(0); opacity: 1; }
}

.form-check-input:checked {
  animation: switchSlide 0.3s ease-out;
}

/* Desktop navbar layout - restore proper positioning */
@media (min-width: 992px) {
  .navbar .container-fluid {
    display: flex;
    align-items: center;
  }

  /* Logo on the left */
  .navbar-brand-container {
    flex: 1;
    order: 1;
  }

  /* Navigation in the center */
  .navbar-collapse {
    flex: 1;
    order: 2;
  }

  /* Language toggle on the right */
  .navbar .container-fluid > .navbar-nav {
    flex: 1;
    order: 3;
    justify-content: flex-end;
  }

  /* Hide hamburger menu on desktop */
  .navbar-toggler {
    display: none;
  }
}

/* Mobile navbar layout adjustments */
@media (max-width: 991.98px) {
  /* Container for mobile navbar layout */
  .navbar .container-fluid {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
  }

  /* Center the logo on mobile */
  .navbar-brand-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    flex: none !important;
    z-index: 1;
  }

  /* Position hamburger menu on the left */
  .navbar-toggler {
    order: 1;
    margin-left: 0;
    margin-right: auto;
    z-index: 2;
  }

  /* Position language toggle on the right - now outside collapsible menu */
  .navbar .container-fluid > .navbar-nav {
    order: 3;
    flex: none !important;
    margin-left: auto;
    z-index: 2;
  }

  /* Ensure the language toggle container is properly positioned */
  .navbar .container-fluid > .navbar-nav .language-toggle-container {
    margin-left: auto;
  }

  /* Adjust collapsed menu to appear below the navbar */
  .navbar-collapse {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bs-navbar-bg, #fff);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    order: 4;
    width: 100%;
  }

  /* Reset the center navigation flex properties for mobile */
  .navbar-collapse .navbar-nav.mx-auto {
    flex: none !important;
    margin: 0 !important;
  }

  /* Ensure proper spacing for mobile layout */
  .navbar-brand-container {
    max-width: calc(100% - 120px); /* Account for hamburger + language toggle */
  }

  .navbar-brand img {
    max-width: 80px; /* Slightly smaller logo on mobile if needed */
  }
}

/* From Uiverse.io by iZOXVL */ 
.boton-elegante {
  padding: 15px 30px;
  border: 2px solid #2c2c2c;
  background-color: #1a1a1a;
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 30px;
  transition: all 0.4s ease;
  outline: none;
  position: relative;
  overflow: hidden;
  font-weight: bold;
}

.boton-elegante::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  transform: scale(0);
  transition: transform 0.5s ease;
}

.boton-elegante:hover::after {
  transform: scale(4);
}

.boton-elegante:hover {
  border-color: #666666;
  background: #292929;
}

/* Sticky Navbar Styles */
.navbar {
  transition: all 0.3s ease-in-out;
  z-index: 1030;
}

.navbar.sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(248, 249, 250, 0.95) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease-out;
}

.navbar.sticky .navbar-brand img {
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.navbar.sticky .nav-btn {
  color: rgba(0, 0, 0, 0.7);
}

.navbar.sticky .nav-btn:hover {
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(0, 0, 0, 0.08);
}

.navbar.sticky .nav-btn.active {
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(0, 0, 0, 0.12);
}

/* Animation for sticky navbar appearance */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Body padding to prevent content jump when navbar becomes fixed */
body.navbar-sticky {
  padding-top: 76px; /* Adjust based on your navbar height */
}

/* Smooth transition for body padding */
body {
  transition: padding-top 0.3s ease-in-out;
}


/* From Uiverse.io by vinodjangid07 */
.card {
  width: fit-content;
  height: fit-content;
  background-color: rgb(238, 238, 238);
  display: flex;
  flex-direction: row; /* Ensure horizontal layout */
  align-items: center;
  justify-content: center;
  padding: 25px 25px;
  gap: 20px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.055);
  flex-wrap: nowrap; /* Prevent wrapping to new lines */
}

/* for all social containers*/
.socialContainer {
  width: 52px;
  height: 52px;
  background-color: rgb(44, 44, 44);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition-duration: .3s;
}
/* instagram*/
.containerOne:hover {
  background-color: #d62976;
  transition-duration: .3s;
}
/* twitter*/
.containerTwo:hover {
  background-color: #00acee;
  transition-duration: .3s;
}
/* linkdin*/
.containerThree:hover {
  background-color: #0072b1;
  transition-duration: .3s;
}
/* Whatsapp*/
.containerFour:hover {
  background-color: #128C7E;
  transition-duration: .3s;
}

.socialContainer:active {
  transform: scale(0.9);
  transition-duration: .3s;
}

.socialSvg {
  width: 17px;
}

.socialSvg path {
  fill: rgb(255, 255, 255);
}

.socialContainer:hover .socialSvg {
  animation: slide-in-top 0.3s both;
}

@keyframes slide-in-top {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Ensure social media links stay horizontal on all screen sizes */
@media (max-width: 768px) {
  .card {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 15px; /* Slightly smaller gap on mobile */
    padding: 20px 15px; /* Adjust padding for mobile */
  }

  .socialContainer {
    width: 45px; /* Slightly smaller icons on mobile */
    height: 45px;
  }
}

@media (max-width: 480px) {
  .card {
    gap: 10px; /* Even smaller gap on very small screens */
    padding: 15px 10px;
  }

  .socialContainer {
    width: 40px; /* Even smaller icons on very small screens */
    height: 40px;
  }
}

